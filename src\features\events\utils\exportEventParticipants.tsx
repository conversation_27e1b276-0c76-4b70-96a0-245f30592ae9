import * as XLSX from 'xlsx'
import toast from '@/shared/components/ui/toast'
import Notification from '@/shared/components/ui/Notification'
import type { Event } from '../types'

const formatDate = (dateString: string) => {
    const [year, month, day] = dateString.split('-')
    return `${day}/${month}/${year}`
}

const formatTime = (timeValue: string | Date | unknown) => {
    if (!timeValue) return 'No especificado'

    let timeString: string

    if (timeValue instanceof Date) {
        const hours = timeValue.getHours().toString().padStart(2, '0')
        const minutes = timeValue.getMinutes().toString().padStart(2, '0')
        timeString = `${hours}:${minutes}`
    } else if (typeof timeValue === 'string') {
        timeString = timeValue
    } else {
        console.warn('formatTime received an unexpected type:', timeValue)
        return 'Hora inválida'
    }

    try {
        const [hours, minutes] = timeString.split(':')
        if (hours === undefined || minutes === undefined) {
            console.warn('formatTime received an invalid time string format:', timeString)
            return 'Hora inválida'
        }
        const hour = parseInt(hours, 10)
        if (isNaN(hour)) {
            console.warn('formatTime could not parse hour:', hours)
            return 'Hora inválida'
        }
        const ampm = hour >= 12 ? 'PM' : 'AM'
        const hour12 = hour % 12 || 12
        return `${hour12.toString().padStart(2, '0')}:${minutes} ${ampm}`
    } catch (e) {
        console.error('Error splitting time string in formatTime:', timeString, e)
        return 'Hora inválida'
    }
}

export const exportEventParticipants = (event: Event) => {
    try {
        const eventInfo = [
            {
                Título: event.title,
                Asunto: event.subject || '',
                Tema: event.topic || '',
                Fecha: formatDate(event.date),
                Hora: `${formatTime(event.startTime)} - ${formatTime(event.endTime)}`,
                Ubicación: event.location,
                Estado: event.status,
                'Total Invitados': event.participantsInvited.length,
            },
        ]

        const participantsData = event.participantsInvited.map((p) => {
            const record = event.attendanceRecords?.find(
                (r) => r.person.id.toString() === p.id.toString(),
            )

            const baseData = {
                Nombre: `${p.firstName} ${p.lastName}`,
                Correo: p.email,
                Rol: p.ecclesiasticalRole || '',
                Estado: record
                    ? record.attended
                        ? 'Asistió'
                        : 'No asistió'
                    : 'Pendiente',
            }

            // Verificar si alguna sesión usa modo manual para incluir campos adicionales
            const hasManualSession = event.sessions?.some(session => session.attendanceMode === 'normal')

            if (hasManualSession) {
                return {
                    ...baseData,
                    Comentario: record?.notes || '',
                    'Asistencia Parcial': record?.partialAttendance ? 'Sí' : 'No',
                    'Tiempo de Permanencia': record?.timeStayed || '',
                    'Comentario asistencia parcial': record?.partialAttendanceComment || '',
                }
            }

            return baseData
        })

        const workbook = XLSX.utils.book_new()
        const infoSheet = XLSX.utils.json_to_sheet(eventInfo)
        const participantsSheet = XLSX.utils.json_to_sheet(participantsData)

        XLSX.utils.book_append_sheet(workbook, infoSheet, 'Evento')
        XLSX.utils.book_append_sheet(workbook, participantsSheet, 'Participantes')

        XLSX.writeFile(workbook, `Evento_${event.id}.xlsx`)
    } catch (error) {
        console.error('Error al exportar participantes:', error)
        toast.push(
            <Notification title="Error" type="danger">
                {error instanceof Error
                    ? error.message
                    : 'Ocurrió un error al exportar los participantes.'}
            </Notification>,
        )
    }
}

export default exportEventParticipants
