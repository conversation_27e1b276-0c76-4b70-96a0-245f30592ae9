import Dialog from '@/shared/components/ui/Dialog'
import Button from '@/shared/components/ui/Button'
import Table from '@/shared/components/ui/Table'
import { useNavigate } from 'react-router-dom'
import useNavigationContext, { type NavigationContext, type NavigationOrigin } from '@/shared/hooks/useNavigationContext'
import type { Event } from '../../types'

interface Props {
    isOpen: boolean
    onClose: () => void
    event: Event | null
    navigationOrigin?: NavigationOrigin
    returnPath?: string
}

const { THead, TBody, Tr, Th, Td } = Table

const SelectSessionModal = ({ isOpen, onClose, event, navigationOrigin, returnPath }: Props) => {
    const navigate = useNavigate()
    const { navigateToAttendance } = useNavigationContext()

    console.log('🔍 DEBUG SelectSessionModal - isOpen:', isOpen)
    console.log('🔍 DEBUG SelectSessionModal - event:', event?.title)
    console.log('🔍 DEBUG SelectSessionModal - sessions:', event?.sessions)
    console.log('🔍 DEBUG SelectSessionModal - navigationOrigin:', navigationOrigin)
    console.log('🔍 DEBUG SelectSessionModal - returnPath:', returnPath)

    const handleGo = (sessionId: string | number) => {
        if (!event) return

        // Encontrar la sesión específica para determinar su configuración
        const session = event.sessions?.find(s => s.id === sessionId)

        // Usar el contexto de navegación pasado como props
        if (navigationOrigin && returnPath) {
            // Si tenemos contexto, usarlo para preservar el origen
            if (session?.attendanceMode === 'kiosk') {
                navigateToAttendance(
                    `/events/${event.id}/sessions/${sessionId}/asistencia-rapida`,
                    navigationOrigin,
                    returnPath,
                    event.id.toString(),
                    sessionId.toString()
                )
            } else {
                navigateToAttendance(
                    `/events/${event.id}/sessions/${sessionId}/asistencia`,
                    navigationOrigin,
                    returnPath,
                    event.id.toString(),
                    sessionId.toString()
                )
            }
        } else {
            // Fallback: usar navegación normal si no hay contexto
            console.warn('🔍 DEBUG SelectSessionModal - No navigation context provided, using fallback')
            if (session?.attendanceMode === 'kiosk') {
                navigate(`/events/${event.id}/sessions/${sessionId}/asistencia-rapida`)
            } else {
                navigate(`/events/${event.id}/sessions/${sessionId}/asistencia`)
            }
        }

        onClose()
    }

    return (
        <Dialog isOpen={isOpen} onClose={onClose} onRequestClose={onClose} width={500} title="Seleccionar Sesión">
            {event ? (
                <Table>
                    <THead>
                        <Tr>
                            <Th>Fecha</Th>
                            <Th>Comentario</Th>
                            <Th>Modo</Th>
                            <Th></Th>
                        </Tr>
                    </THead>
                    <TBody>
                        {event.sessions?.map((s) => (
                            <Tr key={s.id}>
                                <Td>{s.date}</Td>
                                <Td>{s.comment}</Td>
                                <Td>
                                    <span className={`px-2 py-1 rounded text-xs ${
                                        s.attendanceMode === 'kiosk'
                                            ? 'bg-blue-100 text-blue-800'
                                            : 'bg-gray-100 text-gray-800'
                                    }`}>
                                        {s.attendanceMode === 'kiosk' ? 'Kiosco' : 'Normal'}
                                    </span>
                                </Td>
                                <Td className="text-right">
                                    <Button size="sm" variant="twoTone" onClick={() => handleGo(s.id)}>
                                        Abrir
                                    </Button>
                                </Td>
                            </Tr>
                        ))}
                    </TBody>
                </Table>
            ) : (
                <p className="p-4">Sin datos</p>
            )}
        </Dialog>
    )
}

export default SelectSessionModal
