/**
 * Vista de formulario para crear o editar una reunión
 */
import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { FormContainer, FormItem } from '@/shared/components/ui/Form'
import Input from '@/shared/components/ui/Input'
import Button from '@/shared/components/ui/Button'
import Card from '@/shared/components/ui/Card'
import Select from '@/shared/components/ui/Select'
import DatePicker from '@/shared/components/ui/DatePicker'
import TimeInput from '@/shared/components/ui/TimeInput'
import Checkbox from '@/shared/components/ui/Checkbox'
import Dialog from '@/shared/components/ui/Dialog'
import Avatar from '@/shared/components/ui/Avatar'
import Upload from '@/shared/components/ui/Upload'
import Switcher from '@/shared/components/ui/Switcher'
import Radio from '@/shared/components/ui/Radio'
import {
    HiArrowLeft,
    HiSave,
    HiOutlineDocumentDownload,
    HiUserAdd,
    HiX<PERSON>ircle,
    HiSearch,
    HiRefresh
} from 'react-icons/hi'
import { Field, Form, Formik, useFormikContext } from 'formik'
import * as Yup from 'yup'
import * as XLSX from 'xlsx'
import { mockMeetings as mockEvents } from '@/mock/data/eventsData'
import { mockMeetingParticipants, mockHierarchicalData } from '@/mock/data/churchUsersData'
import type {
    Event,
    EventParticipant,
    EventStatus,
    EventType,
    HierarchicalFilterData
} from '../../types'
import toast from '@/shared/components/ui/toast'
import Notification from '@/shared/components/ui/Notification'
import EventsService from '../../services/EventsService'
import SessionTable from '../../components/SessionTable'

// Componente que sincroniza campos según el tipo seleccionado
const TypeWatcher = ({ eventTypes }: { eventTypes: EventType[] }) => {
    const { values, setFieldValue } = useFormikContext<any>()
    const [previousTypeId, setPreviousTypeId] = useState(values.typeId)

    useEffect(() => {
        const selected = eventTypes.find(
            (t) => t.id.toString() === values.typeId?.toString(),
        )

        // Aplicar valores por defecto cuando cambia el tipo de evento
        if (selected && values.typeId !== previousTypeId) {
            // Siempre establecer los valores por defecto del tipo seleccionado
            setFieldValue('autoRegistration', selected.autoRegistrationDefault)

            // Aplicar el método de asistencia del tipo a todas las sesiones
            if (values.sessions && values.sessions.length > 0) {
                const attendanceMode = selected.attendanceMethodDefault === 'kiosco_rapido' ? 'kiosk' : 'normal'
                const updatedSessions = values.sessions.map((session: any) => ({
                    ...session,
                    attendanceMode
                }))
                setFieldValue('sessions', updatedSessions)
            }

            // Resetear las banderas de modificación manual para permitir nuevas modificaciones
            setFieldValue('_userModifiedAutoRegistration', false)

            setPreviousTypeId(values.typeId)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [values.typeId])

    return null
}

// Componente que sincroniza la fecha del evento con la sesión por defecto
const DateSyncWatcher = () => {
    const { values, setFieldValue } = useFormikContext<any>()
    const [previousDate, setPreviousDate] = useState(values.date)

    useEffect(() => {
        // Sincronizar fecha del evento con la sesión por defecto cuando cambia la fecha
        if (values.date !== previousDate && values.sessions && values.sessions.length > 0) {
            const updatedSessions = values.sessions.map((session: any) => {
                // Solo actualizar la fecha de la sesión por defecto
                if (session.isDefault) {
                    return {
                        ...session,
                        date: values.date
                    }
                }
                return session
            })
            setFieldValue('sessions', updatedSessions)
            setPreviousDate(values.date)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [values.date])

    return null
}

// Esquema de validación para el formulario
const validationSchema = Yup.object().shape({
    title: Yup.string().required('El título es obligatorio'),
    date: Yup.string().required('La fecha de inicio es obligatoria'),
    endDate: Yup.string().when('isMultiDay', {
        is: true,
        then: (schema) => schema.required('La fecha de fin es obligatoria para eventos de múltiples días')
            .test('is-after-start', 'La fecha de fin debe ser posterior a la fecha de inicio', function(value) {
                const { date } = this.parent;
                if (!value || !date) return true;
                return new Date(value) >= new Date(date);
            }),
        otherwise: (schema) => schema.notRequired()
    }),
    isMultiDay: Yup.boolean(),
    startTime: Yup.string().required('La hora de inicio es obligatoria'),
    endTime: Yup.string().required('La hora de finalización es obligatoria'),
    location: Yup.string().required('La ubicación es obligatoria'),
    status: Yup.string().required('El estado es obligatorio'),
})

// Opciones para el estado de la reunión
const statusOptions = [
    { value: 'programada', label: 'Programada' },
    { value: 'en-progreso', label: 'En Progreso' },
    { value: 'completada', label: 'Completada' },
    { value: 'cancelada', label: 'Cancelada' },
]

/**
 * Componente para el formulario de creación/edición de reuniones
 */
const EventFormView = () => {
    // Obtener el ID de la reunión de los parámetros de la URL (si existe)
    const { id } = useParams<{ id: string }>()

    // Estado para determinar si es una edición o creación
    const isEditMode = Boolean(id)

    // Estado para almacenar la reunión a editar
    const [eventData, setEventData] = useState<Event | null>(null)

    // Estado para los participantes seleccionados
    const [selectedParticipants, setSelectedParticipants] = useState<EventParticipant[]>([])

    // Lista de tipos de evento disponibles
    const [eventTypes, setEventTypes] = useState<EventType[]>([])

    // Estado para el modal de añadir participantes
    const [isModalOpen, setIsModalOpen] = useState(false)
    // Estado para el modal de importar participantes
    const [isImportModalOpen, setisImportModalOpen] = useState(false)

    // Estados para los filtros del modal
    const [searchTerm, setSearchTerm] = useState('')
    const [selectedField, setSelectedField] = useState<string | number | null>(null)
    const [selectedZone, setSelectedZone] = useState<string | number | null>(null)
    const [selectedDistrict, setSelectedDistrict] = useState<string | number | null>(null)
    const [selectedChurch, setSelectedChurch] = useState<string | number | null>(null)
    const [selectedRole, setSelectedRole] = useState<string | number | null>(null)

    // Estado para los participantes filtrados en el modal
    const [filteredModalParticipants, setFilteredModalParticipants] = useState<EventParticipant[]>([])

    // Estado para los participantes seleccionados en el modal
    const [modalSelectedParticipants, setModalSelectedParticipants] = useState<string[]>([])

    // Estado para controlar la carga de datos
    const [loading, setLoading] = useState(isEditMode)

    // Hook de navegación
    const navigate = useNavigate()

    // Cargar la reunión si estamos en modo edición
    useEffect(() => {
        if (isEditMode && id) {
            loadEventForEdit(id)
        }
        const fetchEventTypes = async () => {
            try {
                const types = await EventsService.getEventTypes()
                setEventTypes(types)
            } catch (error) {
                console.error('Error al cargar tipos de evento:', error)
            }
        }

        fetchEventTypes()
    }, [isEditMode, id])

    // Cargar datos de la reunión para editar
    const loadEventForEdit = async (eventId: string) => {
        setLoading(true)

        try {
            // Obtener la reunión usando el servicio
            const event = await EventsService.getEventById(eventId)

            if (event) {
                setEventData(event)
                setSelectedParticipants(event.participantsInvited || [])
            }

            setLoading(false)
        } catch (error) {
            console.error('Error al cargar la reunión:', error)
            setLoading(false)
        }
    }

    // Opciones de departamentos
    const departmentOptions = [
        { value: 'Presidencia', label: 'Presidencia' },
        { value: 'Secretaría ejecutiva', label: 'Secretaría ejecutiva' },
        { value: 'Tesorería', label: 'Tesorería' },
        { value: 'Contabilidad', label: 'Contabilidad' },
        { value: 'Ministerios', label: 'Ministerios' },
        { value: 'Personales', label: 'Personales' },
        { value: 'Secretaría Ministerial', label: 'Secretaría Ministerial' },
        { value: 'Ministerio de Mayordomia', label: 'Ministerio de Mayordomia' },
        { value: 'Recursos Humanos', label: 'Recursos Humanos' },
        { value: 'Ministerio Juvenil', label: 'Ministerio Juvenil' },
        { value: 'Ministerio de la Mujer', label: 'Ministerio de la Mujer' },
        { value: 'Ministerio Infantil', label: 'Ministerio Infantil' },
        { value: 'Dorcas y Diaconisas', label: 'Dorcas y Diaconisas' },
        { value: 'Ministerio de la Familia', label: 'Ministerio de la Familia' },
        { value: 'Ministerio de Salud', label: 'Ministerio de Salud' },
        { value: 'Repara', label: 'Repara' },
        { value: 'Ministerio de Educación', label: 'Ministerio de Educación' },
        { value: 'Club de Guias Mayores', label: 'Club de Guias Mayores' },
        { value: 'Club de Conquistadores', label: 'Club de Conquistadores' },
        { value: 'Club de Aventureros', label: 'Club de Aventureros' },
        { value: 'SOASEF', label: 'SOASEF' },
        { value: 'Espiritu de Profecia', label: 'Espiritu de Profecia' },
        { value: 'Ancianos', label: 'Ancianos' }
    ];

    // Valores iniciales para el formulario
    const getInitialValues = () => {
        if (isEditMode && eventData) {
            return {
                title: eventData.title || '',
                subject: eventData.subject || '',
                topic: eventData.topic || '',
                description: eventData.description || '',
                date: eventData.date || '',
                endDate: eventData.endDate || '',
                isMultiDay: eventData.isMultiDay || false,
                startTime: eventData.startTime || '',
                endTime: eventData.endTime || '',
                location: eventData.location || '',
                status: eventData.status || 'programada' as EventStatus,
                sendNotifications: eventData.sendNotifications || false,
                invitingDepartment: eventData.invitingDepartment || '',
                typeId: eventData.type?.id || '',
                autoRegistration:
                    typeof eventData.autoRegistration === 'boolean'
                        ? eventData.autoRegistration
                        : eventData.type?.autoRegistrationDefault || false,
                // Campos de seguimiento de modificaciones del usuario
                _userModifiedAutoRegistration: false, // Permitir que TypeWatcher establezca valores por defecto
                sessions: eventData.sessions || [
                    {
                        id: 'default_session',
                        date: eventData.date || new Date().toISOString().split('T')[0],
                        comment: 'Registro de asistencia – Por defecto',
                        attendanceMode: 'normal',
                        isDefault: true,
                        attendanceRecords: [],
                    },
                ],
            }
        }

        return {
            title: '',
            subject: '',
            topic: '',
            description: '',
            date: '',
            endDate: '',
            isMultiDay: false,
            startTime: '',
            endTime: '',
            location: '',
            status: 'programada' as EventStatus,
            sendNotifications: true,
            invitingDepartment: '',
            typeId: '',
            autoRegistration: false,
            // Campos de seguimiento de modificaciones del usuario
            _userModifiedAutoRegistration: false,
            sessions: [
                {
                    id: 'default_session',
                    date: new Date().toISOString().split('T')[0],
                    comment: 'Registro de asistencia – Por defecto',
                    attendanceMode: 'normal',
                    isDefault: true,
                    attendanceRecords: [],
                },
            ],
        }
    }

    // Manejar el envío del formulario
    const handleSubmit = async (values: ReturnType<typeof getInitialValues>) => {
        try {
            // Asegurar que la fecha sea un string en formato YYYY-MM-DD
            let dateString = values.date;
            if (values.date instanceof Date) {
                dateString = values.date.toISOString().split('T')[0];
            }

            // Crear objeto de reunión asegurando que los participantes se incluyan correctamente
            const { typeId, ...rest } = values
            const selectedType = eventTypes.find(
                (t) => t.id.toString() === typeId?.toString(),
            )

            const eventToSave: Partial<Event> = {
                ...rest,
                type: selectedType,
                date: dateString, // Asegurar que la fecha sea string
                participantsInvited: selectedParticipants, // Aseguramos que los participantes seleccionados se incluyan
                invitingDepartment: values.invitingDepartment,
                sessions: values.sessions,
            }

            console.log('Guardando reunión con participantes:', selectedParticipants.length)

            if (isEditMode && id) {
                // Actualizar reunión existente usando el servicio
                const updatedEvent = await EventsService.updateEvent(id, eventToSave)

                if (updatedEvent) {
                    console.log('Reunión actualizada:', updatedEvent)
                    console.log('Participantes guardados:', updatedEvent.participantsInvited?.length || 0)

                    toast.push(
                        <Notification title="Reunión actualizada" type="success">
                            La reunión ha sido actualizada correctamente con {selectedParticipants.length} participantes.
                        </Notification>
                    )
                }
            } else {
                // Crear nueva reunión usando el servicio
                const newEvent = await EventsService.createEvent(eventToSave as Omit<Event, 'id'>)

                console.log('Nueva reunión creada:', newEvent)
                console.log('Participantes guardados:', newEvent.participantsInvited?.length || 0)

                toast.push(
                    <Notification title="Reunión creada" type="success">
                        La reunión ha sido creada correctamente con {selectedParticipants.length} participantes.
                    </Notification>
                )
            }

            // Navegar a la lista de reuniones
            navigate('/events/list')
        } catch (error) {
            console.error('Error al guardar la reunión:', error)

            toast.push(
                <Notification title="Error" type="danger">
                    Ocurrió un error al guardar la reunión. Por favor, inténtelo de nuevo.
                </Notification>
            )
        }
    }

    // Descargar plantilla de Excel para importar participantes
    const handleDownloadTemplate = () => {
        // Crear datos de ejemplo para la plantilla
        const templateData = [
            {
                'Nombre': 'Juan',
                'Apellido': 'Pérez',
                'Email': '<EMAIL>',
                'Teléfono': '+1234567890',
                'Cargo Eclesiástico': 'Pastor',
                'Campo': 'Campo Central',
                'Zona': 'Zona Norte',
                'Distrito': 'Distrito 1',
                'Iglesia': 'Iglesia Central'
            },
            {
                'Nombre': 'María',
                'Apellido': 'González',
                'Email': '<EMAIL>',
                'Teléfono': '+0987654321',
                'Cargo Eclesiástico': 'Anciano',
                'Campo': 'Campo Sur',
                'Zona': 'Zona Sur',
                'Distrito': 'Distrito 2',
                'Iglesia': 'Iglesia del Valle'
            }
        ]

        // Crear libro de trabajo de Excel
        const wb = XLSX.utils.book_new()
        const ws = XLSX.utils.json_to_sheet(templateData)

        // Ajustar el ancho de las columnas
        const colWidths = [
            { wch: 15 }, // Nombre
            { wch: 15 }, // Apellido
            { wch: 25 }, // Email
            { wch: 15 }, // Teléfono
            { wch: 20 }, // Cargo Eclesiástico
            { wch: 15 }, // Campo
            { wch: 15 }, // Zona
            { wch: 15 }, // Distrito
            { wch: 20 }  // Iglesia
        ]
        ws['!cols'] = colWidths

        // Agregar la hoja al libro
        XLSX.utils.book_append_sheet(wb, ws, 'Plantilla Participantes')

        // Descargar el archivo
        XLSX.writeFile(wb, 'plantilla_participantes.xlsx')

        toast.push(
            <Notification title="Plantilla descargada" type="success">
                La plantilla de Excel ha sido descargada correctamente.
            </Notification>
        )
    }

    // Validar y procesar participantes importados desde Excel
    const validateAndProcessParticipants = async (importedData: any[]) => {
        const processedParticipants: EventParticipant[] = []
        const errors: string[] = []
        let newUsersCreated = 0
        let existingUsersAdded = 0

        for (let i = 0; i < importedData.length; i++) {
            const row = importedData[i]
            const rowNumber = i + 2 // +2 porque Excel empieza en 1 y hay encabezados

            // Validar campos obligatorios
            if (!row['Email'] || !row['Nombre'] || !row['Apellido']) {
                errors.push(`Fila ${rowNumber}: Email, Nombre y Apellido son obligatorios`)
                continue
            }

            // Validar formato de email
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            if (!emailRegex.test(row['Email'])) {
                errors.push(`Fila ${rowNumber}: Formato de email inválido`)
                continue
            }

            try {
                // Buscar usuario existente por email y teléfono
                const existingUser = mockMeetingParticipants.find(
                    participant => 
                        participant.email.toLowerCase() === row['Email'].toLowerCase() ||
                        (row['Teléfono'] && participant.phone === row['Teléfono'])
                )

                if (existingUser) {
                    // Usuario existe, solo agregarlo a la reunión si no está ya seleccionado
                    if (!selectedParticipants.some(p => p.id === existingUser.id)) {
                        processedParticipants.push(existingUser)
                        existingUsersAdded++
                    }
                } else {
                    // Usuario no existe, crear nuevo participante
                    const newParticipant: EventParticipant = {
                        id: `new_${Date.now()}_${i}`, // ID temporal para nuevos usuarios
                        firstName: row['Nombre'],
                        lastName: row['Apellido'],
                        email: row['Email'],
                        phone: row['Teléfono'] || '',
                        ecclesiasticalRole: row['Cargo Eclesiástico'] || 'Miembro',
                        avatar: { url: '/img/avatars/default.jpg' },
                        // Asignar IDs por defecto o buscar en datos jerárquicos
                        fieldAssignmentId: 1, // Por defecto
                        zoneAssignmentId: 1,
                        districtAssignmentId: 1,
                        churchAssignmentId: 1,
                        isActive: true
                    }

                    processedParticipants.push(newParticipant)
                    newUsersCreated++
                }
            } catch (error) {
                errors.push(`Fila ${rowNumber}: Error al procesar participante`)
            }
        }

        return {
            participants: processedParticipants,
            errors,
            stats: {
                newUsersCreated,
                existingUsersAdded,
                totalProcessed: processedParticipants.length
            }
        }
    }

    // Manejar subida de archivo para importar
    const handleFileChange = async (files: File[]) => {
        const file = files[0]
        const reader = new FileReader()
        
        reader.onload = async (evt) => {
            try {
                const bstr = evt.target?.result
                const wb = XLSX.read(bstr as string, { type: 'binary' })
                const wsname = wb.SheetNames[0]
                const ws = wb.Sheets[wsname]
                const data = XLSX.utils.sheet_to_json(ws)

                if (data.length === 0) {
                    toast.push(
                        <Notification title="Archivo vacío" type="warning">
                            El archivo Excel no contiene datos para importar.
                        </Notification>
                    )
                    return
                }

                // Validar y procesar participantes
                const result = await validateAndProcessParticipants(data)

                if (result.errors.length > 0) {
                    // Mostrar errores pero continuar con los participantes válidos
                    console.warn('Errores durante la importación:', result.errors)
                    toast.push(
                        <Notification title="Importación con errores" type="warning">
                            Se encontraron {result.errors.length} errores. Revise la consola para más detalles.
                        </Notification>
                    )
                }

                if (result.participants.length > 0) {
                    // Agregar participantes válidos
                    setSelectedParticipants(prev => {
                        const existingIds = prev.map(p => p.id)
                        const newParticipants = result.participants.filter(p => !existingIds.includes(p.id))
                        return [...prev, ...newParticipants]
                    })

                    toast.push(
                        <Notification title="Importación exitosa" type="success">
                            {result.stats.totalProcessed} participantes procesados: 
                            {result.stats.existingUsersAdded} usuarios existentes añadidos, 
                            {result.stats.newUsersCreated} nuevos usuarios creados.
                        </Notification>
                    )
                } else {
                    toast.push(
                        <Notification title="Sin participantes válidos" type="warning">
                            No se pudieron procesar participantes válidos del archivo.
                        </Notification>
                    )
                }

                setisImportModalOpen(false)
            } catch (e) {
                console.error('Error al parsear el archivo Excel', e)
                toast.push(
                    <Notification title="Error de importación" type="danger">
                        No se pudo parsear el archivo. Verifique que sea un archivo Excel válido.
                    </Notification>
                )
            }
        }
        
        reader.readAsBinaryString(file)
    }

    // Manejar el envío del formulario
    const handleSubmit_old = async (values: ReturnType<typeof getInitialValues>) => {
        try {
            let dateString = values.date
            if (values.date instanceof Date) {
                dateString = values.date.toISOString().split('T')[0]
            }

            const eventToSave: Partial<Event> = {
                ...values,
                date: dateString,
                participantsInvited: selectedParticipants,
                invitingDepartment: values.invitingDepartment,
            }

            console.log('Guardando reunión con participantes:', selectedParticipants.length)

            if (isEditMode && id) {
                const updatedEvent = await EventsService.updateEvent(id, eventToSave)

                if (updatedEvent) {
                    console.log('Reunión actualizada:', updatedEvent)
                    console.log('Participantes guardados:', updatedEvent.participantsInvited?.length || 0)

                    toast.push(
                        <Notification title="Reunión actualizada" type="success">
                            La reunión ha sido actualizada correctamente con {selectedParticipants.length} participantes.
                        </Notification>,
                    )
                }
            } else {
                const newEvent = await EventsService.createEvent(eventToSave as Omit<Event, 'id'>)

                console.log('Nueva reunión creada:', newEvent)
                console.log('Participantes guardados:', newEvent.participantsInvited?.length || 0)

                toast.push(
                    <Notification title="Reunión creada" type="success">
                        La reunión ha sido creada correctamente con {selectedParticipants.length} participantes.
                    </Notification>,
                )
            }

            navigate('/events/list')
        } catch (error) {
            console.error('Error al guardar la reunión:', error)

            toast.push(
                <Notification title="Error" type="danger">
                    Ocurrió un error al guardar la reunión. Por favor, inténtelo de nuevo.
                </Notification>,
            )
        }
    }

    // Abrir el modal de añadir participantes
    // NOTA: En un entorno de producción, es recomendable cargar los datos
        // solo cuando se apliquen filtros para mejorar el rendimiento.
        // Para el prototipo, cargamos todos los participantes al abrir el modal.
        // Load all mock participants initially for the prototype
    const handleOpenModal = (e?: React.MouseEvent) => {
        // Prevenir el comportamiento predeterminado si se proporciona un evento
        if (e) {
            e.preventDefault()
            e.stopPropagation()
        }
        setIsModalOpen(true)
        setFilteredModalParticipants([]) // Iniciar vacío para evitar problemas de rendimiento
        setModalSelectedParticipants([])
        // resetModalFilters() // Asegurar que todos los filtros estén limpios
        handleApplyFiltersInModal()
    }

    // Cerrar el modal de añadir participantes
    const handleCloseModal = () => {
        setIsModalOpen(false)
        resetModalFilters()
    }

    // Resetear los filtros del modal
    const resetModalFilters = () => {
        setSearchTerm('')
        setSelectedField(null)
        setSelectedZone(null)
        setSelectedDistrict(null)
        setSelectedChurch(null)
        setSelectedRole(null)
        setFilteredModalParticipants([])
        setModalSelectedParticipants([])
    }
    
    // Manejar cambio en el campo seleccionado
    const handleFieldChange = (fieldId: string | number | null) => {
        setSelectedField(fieldId)
        // Resetear zona, distrito y iglesia cuando cambia el campo
        setSelectedZone(null)
        setSelectedDistrict(null)
        setSelectedChurch(null)
    }
    
    // Manejar cambio en la zona seleccionada
    const handleZoneChange = (zoneId: string | number | null) => {
        setSelectedZone(zoneId)
        // Resetear el distrito y la iglesia seleccionada cuando cambia la zona
        setSelectedDistrict(null)
        setSelectedChurch(null)
    }
    
    // Manejar cambio en el distrito seleccionado
    const handleDistrictChange = (districtId: string | number | null) => {
        setSelectedDistrict(districtId)
        // Resetear la iglesia seleccionada cuando cambia el distrito
        setSelectedChurch(null)
    }

    // Añadir un participante a la reunión
    const handleAddParticipantToEvent = (participant: EventParticipant) => {
        // Verificar si el participante ya está seleccionado
        if (!selectedParticipants.some(p => p.id === participant.id)) {
            setSelectedParticipants([...selectedParticipants, participant])
        }
    }

    // Eliminar un participante de la reunión
    const handleRemoveParticipantFromEvent = (participantId: string | number) => {
        setSelectedParticipants(selectedParticipants.filter(p => p.id !== participantId))
    }

    // Aplicar filtros en el modal
    const handleApplyFiltersInModal = () => {
        let filtered = [...mockMeetingParticipants]

        // Filtrar por término de búsqueda
        if (searchTerm) {
            const term = searchTerm.toLowerCase()
            filtered = filtered.filter(
                participant =>
                    participant.firstName?.toLowerCase().includes(term) ||
                    participant.lastName?.toLowerCase().includes(term) ||
                    participant.email.toLowerCase().includes(term) ||
                    participant.ecclesiasticalRole?.toLowerCase().includes(term)
            )
        }

        // Filtrado jerárquico acumulativo
        // 1. Filtrar por campo
        if (selectedField) {
            filtered = filtered.filter(participant => participant.fieldAssignmentId === selectedField)
        }
        
        // 2. Filtrar por distrito (solo si hay participantes que coincidan con el campo seleccionado)
        if (selectedDistrict) {
            filtered = filtered.filter(participant => participant.districtAssignmentId === selectedDistrict)
        }
        
        // 3. Filtrar por iglesia (solo si hay participantes que coincidan con el distrito seleccionado)
        if (selectedChurch) {
            filtered = filtered.filter(participant => participant.churchAssignmentId === selectedChurch)
        }

        // Filtrar por rol eclesiástico
        if (selectedRole) {
            filtered = filtered.filter(participant => participant.ecclesiasticalRole === selectedRole)
        }

        console.log('Participantes filtrados:', filtered.length)
        setFilteredModalParticipants(filtered)
    }

    // Manejar selección de participante en el modal
    const handleToggleParticipantSelection = (participantId: string) => {
        if (modalSelectedParticipants.includes(participantId)) {
            setModalSelectedParticipants(modalSelectedParticipants.filter(id => id !== participantId))
        } else {
            setModalSelectedParticipants([...modalSelectedParticipants, participantId])
        }
    }

    // Añadir participantes seleccionados desde el modal
    const handleAddSelectedFromModal = () => {
        const participantsToAdd = filteredModalParticipants.filter(
            participant => modalSelectedParticipants.includes(participant.id.toString())
        )

        // Añadir solo los que no están ya seleccionados
        const newParticipants = participantsToAdd.filter(
            participant => !selectedParticipants.some(p => p.id === participant.id)
        )

        setSelectedParticipants([...selectedParticipants, ...newParticipants])
        handleCloseModal()
    }

    // Reabrir una reunión completada
    const handleReopenEvent = () => {
        if (isEditMode && id && eventData?.status === 'completada') {
            // En una implementación real, aquí se haría la llamada a la API
            console.log('Reabriendo reunión:', id)

            // Actualizar el estado local
            setEventData({
                ...eventData,
                status: 'en-progreso'
            })

            toast.push(
                <Notification title="Reunión reabierta" type="success">
                    La reunión ha sido reabierta y ahora está en progreso.
                </Notification>
            )
        }
    }

    // Volver a la lista de reuniones
    const handleBack = () => {
        navigate('/events/list')
    }

    if (loading) {
        return (
            <div className="flex justify-center items-center h-full">
                <p>Cargando información de la reunión...</p>
            </div>
        )
    }

    return (
        <div className="container mx-auto p-4">
            <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                    <Button
                        className="mr-4"
                        icon={<HiArrowLeft />}
                        onClick={handleBack}
                        variant="plain"
                    >
                        Volver al Listado
                    </Button>
                    <h1 className="text-2xl font-bold">
                        {isEditMode ? 'Editar Reunión' : 'Crear Nueva Reunión'}
                    </h1>
                </div>

                {isEditMode && eventData?.status === 'completada' && (
                    <Button
                        variant="solid"
                        color="yellow-500"
                        icon={<HiRefresh />}
                        onClick={handleReopenEvent}
                    >
                        Reabrir Reunión
                    </Button>
                )}
            </div>

            <Formik
                initialValues={getInitialValues()}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
                enableReinitialize
            >
                {({ values, touched, errors, setFieldValue }) => (
                    <Form>
                        <FormContainer>
                            <TypeWatcher eventTypes={eventTypes} />
                            <DateSyncWatcher />
                            {/* Sección de Información de la Reunión */}
                            <Card className="mb-6">
                                <div className="p-6">
                                    <h5 className="mb-4">Información de la Reunión</h5>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <FormItem
                                            label="Título"
                                            invalid={errors.title && touched.title}
                                            errorMessage={errors.title}
                                        >
                                            <Field
                                                type="text"
                                                name="title"
                                                placeholder="Título de la reunión"
                                                component={Input}
                                            />
                                        </FormItem>

                                        <FormItem
                                            label="Asunto/Grupo"
                                            invalid={errors.subject && touched.subject}
                                            errorMessage={errors.subject}
                                        >
                                            <Field
                                                type="text"
                                                name="subject"
                                                placeholder="Asunto o grupo"
                                                component={Input}
                                            />
                                        </FormItem>

                                        <FormItem
                                            label="Departamento que Invita"
                                            invalid={errors.invitingDepartment && touched.invitingDepartment}
                                            errorMessage={errors.invitingDepartment}
                                        >
                                            <Field name="invitingDepartment">
                                                {({ field, form }: any) => (
                                                    <Select
                                                        options={departmentOptions}
                                                        value={departmentOptions.find(
                                                            (option) => option.value === field.value
                                                        )}
                                                        onChange={(option) => {
                                                            form.setFieldValue(field.name, option?.value)
                                                        }}
                                                        placeholder="Seleccionar departamento"
                                                    />
                                                )}
                                            </Field>
                                        </FormItem>

                                        <FormItem
                                            label="Tema"
                                            invalid={errors.topic && touched.topic}
                                            errorMessage={errors.topic}
                                        >
                                            <Field
                                                type="text"
                                                name="topic"
                                                placeholder="Tema específico"
                                                component={Input}
                                            />
                                        </FormItem>

                                        <FormItem
                                            label="Lugar"
                                            invalid={errors.location && touched.location}
                                            errorMessage={errors.location}
                                        >
                                            <Field
                                                type="text"
                                                name="location"
                                                placeholder="Ubicación de la reunión"
                                                component={Input}
                                            />
                                        </FormItem>

                                        <FormItem label="Tipo de Evento">
                                            <Field name="typeId">
                                                {({ field, form }: any) => (
                                                    <Select
                                                        options={eventTypes.map((t) => ({ value: t.id, label: t.name }))}
                                                        value={eventTypes
                                                            .map((t) => ({ value: t.id, label: t.name }))
                                                            .find((opt) => opt.value === field.value)}
                                                        onChange={(option) => {
                                                            form.setFieldValue(field.name, option?.value)
                                                        }}
                                                        placeholder="Seleccionar tipo"
                                                    />
                                                )}
                                            </Field>
                                        </FormItem>

                                        {/* Toggle para eventos de múltiples días */}
                                        <div className="mb-4">
                                            <FormItem label="Evento de múltiples días">
                                                <Field name="isMultiDay">
                                                    {({ field, form }: any) => (
                                                        <Switcher
                                                            checked={field.value}
                                                            onChange={(checked) => {
                                                                form.setFieldValue(field.name, checked)
                                                                // Si se desactiva, limpiar la fecha de fin
                                                                if (!checked) {
                                                                    form.setFieldValue('endDate', '')
                                                                }
                                                            }}
                                                        />
                                                    )}
                                                </Field>
                                            </FormItem>
                                        </div>

                                        {/* Primera fila: Fecha de inicio y Hora de inicio */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <FormItem
                                                label="Fecha de inicio"
                                                invalid={errors.date && touched.date}
                                                errorMessage={errors.date}
                                            >
                                                <Field name="date">
                                                    {({ field, form }: any) => (
                                                        <DatePicker
                                                            placeholder="Seleccionar fecha de inicio"
                                                            value={field.value ? new Date(field.value + 'T00:00:00') : null}
                                                            onChange={(date) => {
                                                                if (date) {
                                                                    const formattedDate = date.toISOString().split('T')[0]
                                                                    form.setFieldValue(field.name, formattedDate)
                                                                }
                                                            }}
                                                        />
                                                    )}
                                                </Field>
                                            </FormItem>

                                            <FormItem
                                                label="Hora de inicio"
                                                invalid={errors.startTime && touched.startTime}
                                                errorMessage={errors.startTime}
                                            >
                                                <Field name="startTime">
                                                    {({ field, form }: any) => (
                                                        <TimeInput
                                                            placeholder="HH:MM"
                                                            format="12"
                                                            amPmPlaceholder="AM/PM"
                                                            value={field.value}
                                                            onChange={(time) => {
                                                                form.setFieldValue(field.name, time)
                                                            }}
                                                        />
                                                    )}
                                                </Field>
                                            </FormItem>
                                        </div>

                                        {/* Segunda fila: Fecha de fin y Hora de fin (condicional) */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            {values.isMultiDay && (
                                                <FormItem
                                                    label="Fecha de fin"
                                                    invalid={errors.endDate && touched.endDate}
                                                    errorMessage={errors.endDate}
                                                >
                                                    <Field name="endDate">
                                                        {({ field, form }: any) => (
                                                            <DatePicker
                                                                placeholder="Seleccionar fecha de fin"
                                                                value={field.value ? new Date(field.value + 'T00:00:00') : null}
                                                                onChange={(date) => {
                                                                    if (date) {
                                                                        const formattedDate = date.toISOString().split('T')[0]
                                                                        form.setFieldValue(field.name, formattedDate)
                                                                    }
                                                                }}
                                                            />
                                                        )}
                                                    </Field>
                                                </FormItem>
                                            )}

                                            <FormItem
                                                label="Hora de fin"
                                                invalid={errors.endTime && touched.endTime}
                                                errorMessage={errors.endTime}
                                            >
                                                <Field name="endTime">
                                                    {({ field, form }: any) => (
                                                        <TimeInput
                                                            placeholder="HH:MM"
                                                            format="12"
                                                            amPmPlaceholder="AM/PM"
                                                            value={field.value}
                                                            onChange={(time) => {
                                                                form.setFieldValue(field.name, time)
                                                            }}
                                                        />
                                                    )}
                                                </Field>
                                            </FormItem>
                                        </div>

                                        {/* Segunda fila: Estado y Registro Automático */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <FormItem
                                                label="Estado"
                                                invalid={errors.status && touched.status}
                                                errorMessage={errors.status}
                                            >
                                                <Field name="status">
                                                    {({ field, form }: any) => (
                                                        <Select
                                                            options={statusOptions}
                                                            value={statusOptions.find(
                                                                (option) => option.value === field.value
                                                            )}
                                                            onChange={(option) => {
                                                                form.setFieldValue(field.name, option?.value)
                                                            }}
                                                        />
                                                    )}
                                                </Field>
                                            </FormItem>

                                            <FormItem label="Registro Automático">
                                                <Field name="autoRegistration">
                                                    {({ field, form }: any) => (
                                                        <Switcher
                                                            checked={field.value}
                                                            onChange={(checked) => {
                                                                form.setFieldValue(field.name, checked)
                                                                // Marcar que el usuario ha modificado este campo manualmente
                                                                form.setFieldValue('_userModifiedAutoRegistration', true)
                                                            }}
                                                        />
                                                    )}
                                                </Field>
                                            </FormItem>
                                        </div>

                                        <FormItem
                                            label="Descripción"
                                            className="md:col-span-2"
                                            invalid={errors.description && touched.description}
                                            errorMessage={errors.description}
                                        >
                                            <Field
                                                type="text"
                                                name="description"
                                                placeholder="Descripción de la reunión"
                                                component={Input}
                                                textArea
                                                rows={4}
                                            />
                                        </FormItem>
                                    </div>
                                </div>
                            </Card>

                            {/* Sección de Sesiones de Asistencia - Movida por encima de participantes */}
                            <Card className="mb-6">
                                <div className="p-6">
                                    <h5 className="mb-4">Sesiones de Asistencia</h5>
                                    <SessionTable
                                        sessions={values.sessions}
                                        onUpdateSession={(idx, session) => {
                                            const updated = [...values.sessions]
                                            updated[idx] = session
                                            setFieldValue('sessions', updated)
                                        }}
                                        onAddSession={() => {
                                            const newSession = {
                                                id: Date.now().toString(),
                                                date: values.date || new Date().toISOString().split('T')[0],
                                                comment: '',
                                                attendanceMode: 'normal' as const,
                                                isDefault: false,
                                                attendanceRecords: [],
                                            }
                                            setFieldValue('sessions', [...values.sessions, newSession])
                                        }}
                                        onDeleteSession={(idx) => {
                                            const updated = values.sessions.filter((_, i) => i !== idx)
                                            setFieldValue('sessions', updated)
                                        }}
                                        eventDates={{ start: values.date, end: values.endTime }}
                                    />
                                </div>
                            </Card>

                            {/* Sección de Participantes */}
                            {values && (
                            <Card className="mb-6 border-2 border-blue-500">
                                <div className="p-6">
                                    <div className="flex flex-wrap justify-between items-center mb-4 gap-2">
                                        <h5 className="text-lg font-bold">Participantes</h5>
                                        <div className="flex gap-2">
                                            <Button
                                                variant="solid"
                                                size="sm"
                                                icon={<HiOutlineDocumentDownload />}
                                                type="button"
                                                onClick={(e) => {
                                                    e.preventDefault()
                                                    e.stopPropagation()
                                                    setisImportModalOpen(true)
                                                }}
                                            >
                                                Importar desde Excel
                                            </Button>
                                            <Button
                                                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4"
                                                variant="solid"
                                                size="sm"
                                                icon={<HiUserAdd />}
                                                type="button"
                                                onClick={handleOpenModal}
                                            >
                                                Añadir Participantes
                                            </Button>
                                        </div>
                                    </div>

                                    {selectedParticipants.length > 0 ? (
                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                            {selectedParticipants.map((participant) => (
                                                <div
                                                    key={participant.id}
                                                    className="flex items-center p-3 border rounded-lg"
                                                >
                                                    <Avatar
                                                        src={typeof participant.avatar === 'string' ? participant.avatar : participant.avatar?.url}
                                                        size={40}
                                                        className="mr-3"
                                                    />
                                                    <div className="flex-grow">
                                                        <div className="font-medium">
                                                            {participant.firstName} {participant.lastName}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            {participant.ecclesiasticalRole || participant.email}
                                                        </div>
                                                    </div>
                                                    <Button
                                                        variant="plain"
                                                        size="sm"
                                                        icon={<HiXCircle />}
                                                        onClick={() => handleRemoveParticipantFromEvent(participant.id)}
                                                        type="button"
                                                    />
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8 text-gray-500">
                                            Aún no se han añadido participantes
                                        </div>
                                    )}

                                    {/* Opción de enviar notificaciones - Movida aquí */}
                                    <div className="mt-6 pt-4 border-t">
                                        <FormItem>
                                            <Field name="sendNotifications">
                                                {({ field, form }: any) => (
                                                    <Checkbox
                                                        checked={field.value}
                                                        onChange={(checked) => {
                                                            form.setFieldValue(field.name, checked)
                                                        }}
                                                    >
                                                        Enviar notificación a los participantes
                                                    </Checkbox>
                                                )}
                                            </Field>
                                        </FormItem>
                                    </div>
                                </div>
                            </Card>
                            )}

                            {/* Botones de Acción */}
                            <div className="flex justify-end">
                                <Button
                                    variant="plain"
                                    className="mr-2"
                                    onClick={handleBack}
                                    type="button"
                                >
                                    Cancelar
                                </Button>
                                <Button
                                    variant="solid"
                                    type="submit"
                                    icon={<HiSave />}
                                >
                                    Guardar Reunión
                                </Button>
                            </div>
                        </FormContainer>
                    </Form>
                    )}
            </Formik>

            {/* Modal de Añadir Participantes */}
            <Dialog
                isOpen={isModalOpen}
                onClose={handleCloseModal}
                onRequestClose={handleCloseModal}
                width={800}
                contentClassName="p-0 flex flex-col"
                className="max-h-[90vh]" // Added Tailwind class for max height on the Dialog itself
            >
                <h4 className="text-lg font-semibold mb-4 px-6 pt-6 sticky top-0 bg-white z-10 border-b pb-4">
                    Añadir Participantes
                </h4>
                <div className="px-6 pb-4 overflow-y-auto flex-grow">
                    {/* Búsqueda Individual */}
                    <div className="mb-6">
                        <h6 className="mb-2">Búsqueda Individual</h6>
                        <div className="flex items-center gap-2">
                            <Input
                                prefix={<HiSearch className="text-lg" />}
                                value={searchTerm}
                                onChange={e => setSearchTerm(e.target.value)}
                                placeholder="Buscar por nombre, email o cargo..."
                                className="flex-grow"
                            />
                            <Button
                                variant="solid"
                                size="sm"
                                onClick={handleApplyFiltersInModal}
                                className="bg-blue-600 hover:bg-blue-700 text-white"
                            >
                                Buscar
                            </Button>
                        </div>
                    </div>

                    {/* Filtros Jerárquicos */}
                    <div className="mb-6">
                        <div className="flex justify-between items-center mb-2">
                            <h6 className="mb-0">Filtrar por Estructura Eclesiástica</h6>
                            <Button
                                variant="link"
                                size="sm"
                                className="text-blue-600 hover:underline p-0 h-auto"
                                onClick={resetModalFilters} // Llama a la función existente para limpiar filtros
                            >
                                Limpiar todos los filtros
                            </Button>
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4 items-end">
                            {/* Campo Eclesiástico */}
                            <div>
                                <label className="form-label mb-2">Campo Eclesiástico</label>
                                <Select
                                    options={[
                                        { value: null, label: 'Todos los Campos' },
                                        ...mockHierarchicalData.fields.map(field => ({
                                            value: field.id,
                                            label: field.name
                                        }))
                                    ]}
                                    value={selectedField === null ? 
                                        { value: null, label: 'Todos los Campos' } : 
                                        mockHierarchicalData.fields
                                            .filter(field => field.id === selectedField)
                                            .map(field => ({ value: field.id, label: field.name }))[0]
                                    }
                                    onChange={option => handleFieldChange(option?.value !== undefined ? option.value : null)}
                                    placeholder="Seleccionar Campo"
                                />
                            </div>
                            {/* Zona Pastoral */}
                            <div>
                                <label className="form-label mb-2">Zona Pastoral</label>
                                <Select
                                    options={[
                                        { value: null, label: 'Todas las Zonas' },
                                        ...mockHierarchicalData.zones
                                            .filter(zone => !selectedField || zone.fieldId === selectedField)
                                            .map(zone => ({
                                                value: zone.id,
                                                label: zone.name
                                            }))
                                    ]}
                                    value={selectedZone === null ? 
                                        { value: null, label: 'Todas las Zonas' } : 
                                        mockHierarchicalData.zones
                                            .filter(zone => zone.id === selectedZone)
                                            .map(zone => ({ value: zone.id, label: zone.name }))[0]
                                    }
                                    onChange={option => handleZoneChange(option?.value !== undefined ? option.value : null)}
                                    placeholder="Seleccionar Zona"
                                    isDisabled={selectedField === null}
                                />
                            </div>
                            {/* Distrito Pastoral */}
                            <div>
                                <label className="form-label mb-2">Distrito Pastoral</label>
                                <Select
                                    options={[
                                        { value: null, label: 'Todos los Distritos' },
                                        ...mockHierarchicalData.districts
                                            .filter(district => !selectedZone || district.zoneId === selectedZone)
                                            .map(district => ({
                                                value: district.id,
                                                label: district.name
                                            }))
                                    ]}
                                    value={selectedDistrict === null ? 
                                        { value: null, label: 'Todos los Distritos' } : 
                                        mockHierarchicalData.districts
                                            .filter(district => district.id === selectedDistrict)
                                            .map(district => ({ value: district.id, label: district.name }))[0]
                                    }
                                    onChange={option => handleDistrictChange(option?.value !== undefined ? option.value : null)}
                                    placeholder="Seleccionar Distrito"
                                    isDisabled={selectedZone === null}
                                />
                            </div>
                            {/* Iglesia Local */}
                            <div>
                                <label className="form-label mb-2">Iglesia Local</label>
                                <Select
                                    options={[
                                        { value: null, label: 'Todas las Iglesias' },
                                        ...mockHierarchicalData.churches
                                            .filter(church => !selectedDistrict || church.districtId === selectedDistrict)
                                            .map(church => ({
                                                value: church.id,
                                                label: church.name
                                            }))
                                    ]}
                                    value={selectedChurch === null ? 
                                        { value: null, label: 'Todas las Iglesias' } : 
                                        mockHierarchicalData.churches
                                            .filter(church => church.id === selectedChurch)
                                            .map(church => ({ value: church.id, label: church.name }))[0]
                                    }
                                    onChange={option => setSelectedChurch(option?.value !== undefined ? option.value : null)}
                                    placeholder="Seleccionar Iglesia"
                                    isDisabled={selectedDistrict === null}
                                />
                            </div>
                            {/* Cargo/Ministerio */}
                            <div>
                                <label className="form-label mb-2">Cargo/Ministerio</label>
                                <Select
                                    options={[
                                        { value: null, label: 'Todos los Cargos' },
                                        ...mockHierarchicalData.ecclesiasticalRoles.map(role => ({
                                            value: role,
                                            label: role
                                        }))
                                    ]}
                                    value={selectedRole === null ? 
                                        { value: null, label: 'Todos los Cargos' } : 
                                        (selectedRole ? { value: selectedRole, label: selectedRole } : null)
                                    }
                                    onChange={option => setSelectedRole(option?.value !== undefined ? option.value : null)}
                                    placeholder="Seleccionar Cargo"
                                />
                            </div>
                            {/* Botón Aplicar Filtros y Buscar Miembros */}
                            <div className="w-full">
                                <Button
                                    variant="solid"
                                    size="sm"
                                    onClick={handleApplyFiltersInModal}
                                    className="bg-blue-600 hover:bg-blue-700 text-white w-full"
                                >
                                    Aplicar Filtros y Buscar Miembros
                                </Button>
                            </div>
                        </div>
                    </div>

                    {/* Resultados de la Búsqueda */}
                    <div>
                        <h6 className="mb-2">Resultados ({filteredModalParticipants.length})</h6>
                        <div className="border rounded-md p-2">
                            {filteredModalParticipants.length > 0 ? (
                                filteredModalParticipants.map(participant => {
                                    const isSelected = modalSelectedParticipants.includes(participant.id.toString())
                                    const isAlreadyAdded = selectedParticipants.some(p => p.id === participant.id)

                                    return (
                                        <div
                                            key={participant.id}
                                            className={`flex items-center p-3 border-b ${isSelected ? 'bg-gray-100' : ''} ${isAlreadyAdded ? 'opacity-50' : ''}`}
                                        >
                                            <Checkbox
                                                className="mr-3"
                                                checked={isSelected}
                                                disabled={isAlreadyAdded}
                                                onChange={() => handleToggleParticipantSelection(participant.id.toString())}
                                            />
                                            <Avatar
                                                src={typeof participant.avatar === 'string' ? participant.avatar : participant.avatar?.url}
                                                size={40}
                                                className="mr-3"
                                            />
                                            <div className="flex-grow">
                                                <div className="font-medium">
                                                    {participant.firstName} {participant.lastName}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    {participant.ecclesiasticalRole || participant.email}
                                                </div>
                                            </div>
                                            {isAlreadyAdded && (
                                                <span className="text-sm text-gray-500">Ya añadido</span>
                                            )}
                                        </div>
                                    )
                                })
                            ) : (
                                <div className="text-center py-4 text-gray-500">
                                    No se encontraron participantes con los filtros aplicados
                                </div>
                            )}
                        </div>
                    </div>
                </div>
                <div className="flex justify-end px-6 py-4 border-t bg-gray-50 sticky bottom-0 z-10">
                    <Button
                        variant="plain"
                        className="mr-2"
                        onClick={handleCloseModal}
                    >
                        Cancelar
                    </Button>
                    <Button
                        variant="solid"
                        onClick={handleAddSelectedFromModal}
                        disabled={modalSelectedParticipants.length === 0}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                        Añadir Seleccionados ({modalSelectedParticipants.length})
                    </Button>
                </div>
            </Dialog>

            {/* Modal de Importar Participantes - Movido fuera del formulario para evitar envío automático */}
            <Dialog
                isOpen={isImportModalOpen}
                onClose={() => setisImportModalOpen(false)}
                onRequestClose={() => setisImportModalOpen(false)}
                width={700}
                title="Importar Participantes desde Excel"
            >
                <form onSubmit={(e) => e.preventDefault()}>
                    <div className="p-6">
                    <div className="mb-6">
                        <h6 className="text-lg font-semibold mb-3">Instrucciones de Importación</h6>
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                            <h7 className="font-medium text-blue-800 mb-2 block">Formato del Archivo Excel:</h7>
                            <ul className="text-sm text-blue-700 space-y-1">
                                <li>• <strong>Nombre:</strong> Nombre del participante (obligatorio)</li>
                                <li>• <strong>Apellido:</strong> Apellido del participante (obligatorio)</li>
                                <li>• <strong>Email:</strong> Correo electrónico (obligatorio, usado para identificar usuarios existentes)</li>
                                <li>• <strong>Teléfono:</strong> Número de teléfono (opcional, usado para identificar usuarios existentes)</li>
                                <li>• <strong>Cargo Eclesiástico:</strong> Rol en la iglesia (opcional)</li>
                                <li>• <strong>Campo, Zona, Distrito, Iglesia:</strong> Estructura eclesiástica (opcional)</li>
                            </ul>
                        </div>
                        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                            <h7 className="font-medium text-green-800 mb-2 block">Lógica de Importación:</h7>
                            <ul className="text-sm text-green-700 space-y-1">
                                <li>• Si el usuario existe (por email o teléfono): se añade a la reunión</li>
                                <li>• Si el usuario no existe: se crea un nuevo usuario y se añade a la reunión</li>
                                <li>• Los usuarios duplicados en la reunión se omiten automáticamente</li>
                            </ul>
                        </div>
                        <div className="flex gap-3">
                            <Button
                                variant="solid"
                                size="sm"
                                icon={<HiOutlineDocumentDownload />}
                                onClick={handleDownloadTemplate}
                                className="bg-green-600 hover:bg-green-700 text-white"
                                type="button"
                            >
                                Descargar Plantilla Excel
                            </Button>
                            <Button
                                variant="plain"
                                size="sm"
                                icon={<HiRefresh />}
                                onClick={() => setisImportModalOpen(false)}
                                type="button"
                            >
                                Cancelar
                            </Button>
                        </div>
                    </div>
                    
                    <div className="border-t pt-4">
                        <h6 className="font-semibold mb-3">Subir Archivo Excel</h6>
                        <p className="mb-4 text-gray-600">Seleccione un archivo .xlsx o .xls para importar la lista de participantes.</p>
                        {/* Envolver el Upload en un div para evitar que interfiera con el formulario principal */}
                        <div 
                            onClick={(e) => {
                                e.preventDefault()
                                e.stopPropagation()
                            }}
                            onSubmit={(e) => {
                                e.preventDefault()
                                e.stopPropagation()
                            }}
                        >
                            <Upload
                                draggable
                                accept=".xlsx, .xls"
                                onChange={(files) => {
                                    handleFileChange(files)
                                }}
                                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors"
                            >
                                <div className="text-gray-500">
                                    <HiOutlineDocumentDownload className="mx-auto text-3xl mb-2" />
                                    <p>Arrastra tu archivo Excel aquí o haz clic para seleccionar</p>
                                    <p className="text-sm mt-1">Formatos soportados: .xlsx, .xls</p>
                                </div>
                            </Upload>
                        </div>
                    </div>
                    </div>
                </form>
            </Dialog>
        </div>
    )
}

export default EventFormView

// ...
    // Formatear fecha (YYYY-MM-DD -> DD/MM/YYYY)
    const formatDate = (dateString: string) => {
        const [year, month, day] = dateString.split('-')
        return `${day}/${month}/${year}`
    }

    // Formatear hora de 24h a 12h con AM/PM
    const formatTime = (timeString: string) => {
        if (!timeString) return 'No especificado'
        
        const [hours, minutes] = timeString.split(':')
        const hour = parseInt(hours, 10)
        const ampm = hour >= 12 ? 'PM' : 'AM'
        const hour12 = hour % 12 || 12
        
        return `${hour12}:${minutes} ${ampm}`
    }
