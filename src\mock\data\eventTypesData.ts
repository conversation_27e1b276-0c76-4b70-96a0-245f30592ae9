import type { EventType } from '@/features/events/types'

export const mockEventTypes: EventType[] = [
    {
        id: 1,
        name: '<PERSON><PERSON><PERSON>',
        autoRegistrationDefault: true,
        attendanceMethodDefault: 'kiosco_rapido',
    },
    {
        id: 2,
        name: 'Congreso',
        autoRegistrationDefault: true,
        attendanceMethodDefault: 'kiosco_rapido',
    },
    {
        id: 3,
        name: 'Encuentro',
        autoRegistrationDefault: true,
        attendanceMethodDefault: 'kiosco_rapido',
    },
    {
        id: 4,
        name: 'Instrucción',
        autoRegistrationDefault: false,
        attendanceMethodDefault: 'manual',
    },
    {
        id: 5,
        name: 'Entrenamiento',
        autoRegistrationDefault: true,
        attendanceMethodDefault: 'kiosco_rapido',
    },
    {
        id: 6,
        name: '<PERSON>urs<PERSON>',
        autoRegistrationDefault: false,
        attendanceMethodDefault: 'manual',
    },
    {
        id: 7,
        name: 'Tall<PERSON>',
        autoRegistrationDefault: true,
        attendanceMethodDefault: 'kiosco_rapido',
    },
    {
        id: 8,
        name: 'Jorna<PERSON>',
        autoRegistrationDefault: true,
        attendanceMethodDefault: 'kiosco_rapido',
    },
    {
        id: 9,
        name: 'Capacitación',
        autoRegistrationDefault: true,
        attendanceMethodDefault: 'kiosco_rapido',
    },
    {
        id: 10,
        name: 'Certificación',
        autoRegistrationDefault: false,
        attendanceMethodDefault: 'manual',
    },
    {
        id: 11,
        name: 'Escuela',
        autoRegistrationDefault: false,
        attendanceMethodDefault: 'manual',
    },
    {
        id: 12,
        name: 'INCALA',
        autoRegistrationDefault: true,
        attendanceMethodDefault: 'kiosco_rapido',
    },
    {
        id: 13,
        name: 'Campamento',
        autoRegistrationDefault: true,
        attendanceMethodDefault: 'kiosco_rapido',
    },
    {
        id: 14,
        name: 'Retiro',
        autoRegistrationDefault: true,
        attendanceMethodDefault: 'kiosco_rapido',
    },
    {
        id: 15,
        name: 'Cumbre',
        autoRegistrationDefault: true,
        attendanceMethodDefault: 'kiosco_rapido',
    },
    {
        id: 16,
        name: 'Camporee',
        autoRegistrationDefault: true,
        attendanceMethodDefault: 'kiosco_rapido',
    },
    {
        id: 17,
        name: 'Seminario',
        autoRegistrationDefault: false,
        attendanceMethodDefault: 'manual',
    },
    {
        id: 18,
        name: 'Seminario taller',
        autoRegistrationDefault: true,
        attendanceMethodDefault: 'kiosco_rapido',
    },
    {
        id: 19,
        name: 'Convención',
        autoRegistrationDefault: true,
        attendanceMethodDefault: 'kiosco_rapido',
    },
    {
        id: 20,
        name: 'Reunión',
        autoRegistrationDefault: false,
        attendanceMethodDefault: 'manual',
    },
    {
        id: 21,
        name: 'Reunión de Instrucción',
        autoRegistrationDefault: false,
        attendanceMethodDefault: 'manual',
    },
    {
        id: 22,
        name: 'Festival',
        autoRegistrationDefault: true,
        attendanceMethodDefault: 'kiosco_rapido',
    },
]

