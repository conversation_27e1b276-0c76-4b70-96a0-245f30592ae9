/**
 * Vista de detalle de una reunión
 * Muestra la información detallada de una reunión y opciones para gestionarla
 */
import { useState, useEffect } from 'react';
import Notification from '@/shared/components/ui/Notification'
import toast from '@/shared/components/ui/toast'
import { useParams, useNavigate } from 'react-router-dom'
import useNavigationContext from '@/shared/hooks/useNavigationContext'
import Card from '@/shared/components/ui/Card'
import Button from '@/shared/components/ui/Button'
import Avatar from '@/shared/components/ui/Avatar'
import Badge from '@/shared/components/ui/Badge'
import {
    HiArrowLeft,
    HiPencil,
    HiUserGroup,
    HiCheck,
    HiX,
    HiCalendar,
    HiClock,
    HiLocationMarker,
    HiInformationCircle
} from 'react-icons/hi'
import { mockMeetings as mockEvents } from '@/mock/data/eventsData'
import type { Event, EventStatus } from '../../types'
import StatusBadge from '../../components/StatusBadge'
import SelectSessionModal from '../../components/SelectSessionModal'
import QRModal from '../../components/QRModal'

/**
 * Componente para la vista de detalle de una reunión
 */
const EventDetailView = () => {
    // Obtener el ID de la reunión de los parámetros de la URL
    const { id } = useParams<{ id: string }>()

    // Estado para almacenar los detalles de la reunión
    const [eventDetail, setEventDetail] = useState<Event | null>(null)

    // Estado para controlar la carga de datos
    const [loading, setLoading] = useState(true)

    // Hook de navegación
    const navigate = useNavigate()
    const [isQrModalOpen, setIsQrModalOpen] = useState(false)

    // Hook de navegación contextual
    const { navigateToAttendance } = useNavigationContext()

    // Cargar los detalles de la reunión al montar el componente
    useEffect(() => {
        const fetchEventDetail = async () => {
            if (!id) return

            try {
                setLoading(true)

                // Simular llamada a la API
                setTimeout(() => {
                    const event = mockEvents.find(m => m.id.toString() === id)
                    setEventDetail(event || null)
                    setLoading(false)
                }, 500)
            } catch (error) {
                console.error('Error al cargar los detalles de la reunión:', error)
                setLoading(false)
            }
        }

        fetchEventDetail()
    }, [id])

    // Navegar a la vista de registro de asistencia
    const handleStartAttendance = () => {
        if (id && eventDetail) {
            // Si el evento tiene más de una sesión, mostrar modal de selección
            if (eventDetail.sessions && eventDetail.sessions.length > 1) {
                // TODO: Implementar modal de selección de sesión para EventDetail
                // Por ahora, usar la primera sesión
                const session = eventDetail.sessions[0]
                if (session.attendanceMode === 'kiosk') {
                    navigateToAttendance(
                        `/events/${id}/sessions/${session.id}/asistencia-rapida`,
                        'event-detail',
                        `/events/${id}`,
                        id,
                        session.id.toString()
                    )
                } else {
                    navigateToAttendance(
                        `/events/${id}/sessions/${session.id}/asistencia`,
                        'event-detail',
                        `/events/${id}`,
                        id,
                        session.id.toString()
                    )
                }
            } else {
                // Si tiene una sola sesión, ir directamente según la configuración de la sesión
                const session = eventDetail.sessions?.[0]
                if (session) {
                    if (session.attendanceMode === 'kiosk') {
                        navigateToAttendance(
                            `/events/${id}/sessions/${session.id}/asistencia-rapida`,
                            'event-detail',
                            `/events/${id}`,
                            id,
                            session.id.toString()
                        )
                    } else {
                        navigateToAttendance(
                            `/events/${id}/sessions/${session.id}/asistencia`,
                            'event-detail',
                            `/events/${id}`,
                            id,
                            session.id.toString()
                        )
                    }
                } else {
                    // Fallback: usar ruta antigua si no hay sesiones
                    console.warn('Evento sin sesiones, usando ruta de fallback')
                    navigateToAttendance(
                        `/events/${id}/attendance`,
                        'event-detail',
                        `/events/${id}`,
                        id
                    )
                }
            }
        }
    }

    // Navegar a la vista de edición de la reunión
    const handleEditEventFromDetail = () => {
        if (id) {
            navigate(`/events/${id}/edit`)
        }
    }

    // Volver a la lista de reuniones
    const handleBack = () => {
        navigate('/events/list')
    }

    // Formatear fecha (YYYY-MM-DD -> DD/MM/YYYY)
    const formatDate = (dateString: string) => {
        const [year, month, day] = dateString.split('-')
        return `${day}/${month}/${year}`
    }

    // Formatear hora de 24h a 12h con AM/PM
    const formatTime = (timeString: string | undefined | null) => {
        if (!timeString || typeof timeString !== 'string') return 'No especificado'

        try {
            const [hours, minutes] = timeString.split(':')
            if (!hours || !minutes) return 'No especificado'

            const hour = parseInt(hours, 10)
            const ampm = hour >= 12 ? 'PM' : 'AM'
            const hour12 = hour % 12 || 12

            return `${hour12}:${minutes} ${ampm}`
        } catch (error) {
            console.error('Error formatting time:', timeString, error)
            return 'No especificado'
        }
    }

    // Estos métodos han sido reemplazados por el componente StatusBadge

    if (loading) {
        return (
            <div className="flex justify-center items-center h-full">
                <p>Cargando detalles de la reunión...</p>
            </div>
        )
    }

    if (!eventDetail) {
        return (
            <div className="flex flex-col items-center justify-center h-full">
                <p className="text-lg mb-4">No se encontró la reunión solicitada</p>
                <Button variant="solid" onClick={handleBack}>
                    Volver a la lista
                </Button>
            </div>
        )
    }

    return (
        <div className="container mx-auto p-4">
            <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                    <Button
                        className="mr-4"
                        icon={<HiArrowLeft />}
                        onClick={handleBack}
                        variant="plain"
                    >
                        Volver al Listado
                    </Button>
                    <h1 className="text-2xl font-bold">
                        Detalles de la Reunión: {eventDetail.title}
                    </h1>
                </div>
            </div>

            {/* Sección de Información General */}
            <Card className="mb-6">
                <div className="p-6">
                    <h5 className="mb-4">Información General</h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div className="mb-4">
                                <div className="font-semibold mb-1">Estado</div>
                                <StatusBadge status={eventDetail.status} />
                            </div>

                            <div className="mb-4">
                                <div className="font-semibold mb-1 flex items-center">
                                    <HiCalendar className="mr-2" />
                                    {eventDetail.isMultiDay ? 'Fechas' : 'Fecha'}
                                </div>
                                <div>
                                    {eventDetail.isMultiDay && eventDetail.endDate ? (
                                        <>
                                            <div><strong>Inicio:</strong> {formatDate(eventDetail.date)}</div>
                                            <div><strong>Fin:</strong> {formatDate(eventDetail.endDate)}</div>
                                        </>
                                    ) : (
                                        formatDate(eventDetail.date)
                                    )}
                                </div>
                            </div>

                            <div className="mb-4">
                                <div className="font-semibold mb-1 flex items-center">
                                    <HiClock className="mr-2" /> Horario
                                </div>
                                <div>
                                    {eventDetail.isMultiDay ? (
                                        <span>
                                            <strong>Horario general:</strong> {formatTime(eventDetail.startTime)} - {formatTime(eventDetail.endTime)}
                                            <br />
                                            <small className="text-gray-500">
                                                (Ver sesiones individuales para horarios específicos)
                                            </small>
                                        </span>
                                    ) : (
                                        `${formatTime(eventDetail.startTime)} - ${formatTime(eventDetail.endTime)}`
                                    )}
                                </div>
                            </div>

                            <div className="mb-4">
                                <div className="font-semibold mb-1 flex items-center">
                                    <HiLocationMarker className="mr-2" /> Lugar
                                </div>
                                <div>{eventDetail.location}</div>
                            </div>
                        </div>

                        <div>
                            {eventDetail.subject && (
                                <div className="mb-4">
                                    <div className="font-semibold mb-1">Asunto/Grupo</div>
                                    <div>{eventDetail.subject}</div>
                                </div>
                            )}

                            {eventDetail.topic && (
                                <div className="mb-4">
                                    <div className="font-semibold mb-1">Tema</div>
                                    <div>{eventDetail.topic}</div>
                                </div>
                            )}

                            <div className="mb-4">
                                <div className="font-semibold mb-1 flex items-center">
                                    <HiInformationCircle className="mr-2" /> Descripción
                                </div>
                                <div>{eventDetail.description || 'Sin descripción'}</div>
                            </div>

                            <div className="mb-4">
                                <div className="font-semibold mb-1">Notificaciones</div>
                                <div>{eventDetail.sendNotifications ? 'Activadas' : 'Desactivadas'}</div>
                            </div>

                            <div className="mb-4">
                                <div className="font-semibold mb-1 flex items-center">
                                    <HiInformationCircle className="mr-2" /> Departamento que Invita
                                </div>
                                <div>{eventDetail.invitingDepartment || 'No especificado'}</div>
                            </div>
                            {eventDetail.autoRegistration && (
                                <div className="mt-4">
                                    <Button variant="solid" onClick={() => setIsQrModalOpen(true)}>
                                        Generar QR/URL de Registro
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </Card>

            {/* Sección de Información de Múltiples Días (solo si aplica) */}
            {eventDetail.isMultiDay && (
                <Card className="mb-6">
                    <div className="p-6">
                        <h5 className="mb-4 flex items-center">
                            <HiCalendar className="mr-2" />
                            Información de Evento de Múltiples Días
                        </h5>
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div className="flex items-center mb-2">
                                <HiInformationCircle className="text-blue-600 mr-2" />
                                <span className="font-semibold text-blue-800">
                                    Este evento se extiende por múltiples días
                                </span>
                            </div>
                            <div className="text-sm text-blue-700">
                                <div><strong>Fecha de inicio:</strong> {formatDate(eventDetail.date)}</div>
                                <div><strong>Fecha de fin:</strong> {formatDate(eventDetail.endDate)}</div>
                                <div className="mt-2">
                                    Las sesiones individuales pueden tener horarios y métodos de asistencia específicos.
                                    Consulta la sección de sesiones para más detalles.
                                </div>
                            </div>
                        </div>
                    </div>
                </Card>
            )}

            {/* Sección de Participantes */}
            <Card className="mb-6">
                <div className="p-6">
                    <div className="flex justify-between items-center mb-4">
                        <h5>Participantes Convocados ({eventDetail.participantsInvited.length})</h5>

                        <div className="flex space-x-2">
                            {eventDetail.status !== 'cancelada' && (
                                <Button
                                    variant="solid"
                                    icon={<HiUserGroup />}
                                    onClick={handleStartAttendance}
                                >
                                    {eventDetail.status === 'completada' ? 'Ver Asistencia' : 'Registrar Asistencia'}
                                </Button>
                            )}

                            {eventDetail.status !== 'completada' && eventDetail.status !== 'cancelada' && (
                                <Button
                                    variant="solid"
                                    icon={<HiPencil />}
                                    onClick={handleEditEventFromDetail}
                                >
                                    Editar Reunión
                                </Button>
                            )}
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {eventDetail.participantsInvited.map((participant) => {
                            // Buscar si hay registro de asistencia para este participante
                            const attendanceRecord = eventDetail.attendanceRecords?.find(
                                record => record.person.id === participant.id
                            )

                            // Determinar si asistió (desde el registro o desde la propiedad del participante)
                            const attended = attendanceRecord?.attended ?? participant.attended

                            return (
                                <div
                                    key={participant.id}
                                    className="flex items-center p-3 border rounded-lg"
                                >
                                    <Avatar
                                        src={typeof participant.avatar === 'string' ? participant.avatar : participant.avatar?.url}
                                        size={40}
                                        className="mr-3"
                                    />
                                    <div className="flex-grow">
                                        <div className="font-medium">
                                            {participant.firstName} {participant.lastName}
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {participant.ecclesiasticalRole || participant.email}
                                        </div>
                                    </div>

                                    {eventDetail.status === 'completada' && attended !== null && (
                                        <div className="ml-2">
                                            {attended ? (
                                                <Badge className="bg-emerald-100 text-emerald-800 border border-emerald-200 px-2.5 py-1 rounded-full text-xs font-medium flex items-center">
                                                    <HiCheck className="mr-1.5" /> Asistió
                                                </Badge>
                                            ) : (
                                                <Badge className="bg-red-100 text-red-800 border border-red-200 px-2.5 py-1 rounded-full text-xs font-medium flex items-center">
                                                    <HiX className="mr-1.5" /> No asistió
                                                </Badge>
                                            )}
                                        </div>
                                    )}
                                </div>
                            )
                        })}
                    </div>
                </div>
            </Card>
            {/* Modal de código QR */}
            {eventDetail && (
                <QRModal
                    isOpen={isQrModalOpen}
                    onClose={() => setIsQrModalOpen(false)}
                    event={eventDetail}
                    title="Compartir Registro de Evento"
                />
            )}
        </div>
    )
}

export default EventDetailView
