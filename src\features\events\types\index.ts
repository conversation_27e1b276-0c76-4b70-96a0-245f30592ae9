/**
 * Tipos para el módulo de eventos
 * Define las interfaces y tipos utilizados en el módulo de eventos
 */
import type { UserProfile } from '@/features/account/types/account';
import type { Media } from '@/shared/types/media';
import type { AttendanceSession } from './attendanceSession';

/**
 * Extiende el tipo User de Strapi (o UserProfile)
 * para incluir los campos eclesiásticos y de asignación.
 * Este tipo representará a un participante de evento.
 */
export interface EventParticipant extends UserProfile {
    // Campos eclesiásticos añadidos al schema 'user' de Strapi
    ecclesiasticalRole?: string; // Puesto/Cargo dentro de la iglesia
    phone?: string;
    
    // IDs de las relaciones. Los objetos completos se obtendrían con populate.
    fieldAssignmentId?: number | string | null; 
    districtAssignmentId?: number | string | null;
    churchAssignmentId?: number | string | null;

    // Para el prototipo, podemos añadir los nombres directamente para facilitar la visualización
    // En una implementación real, estos vendrían de popular las relaciones.
    assignedFieldName?: string | null;
    assignedDistrictName?: string | null;
    assignedChurchName?: string | null;

    // Estado de asistencia para un evento específico (se usa en el contexto de un evento)
    attended?: boolean | null;
}

/**
 * Estados posibles de un evento
 */
export type EventStatus = 'programada' | 'en-progreso' | 'completada' | 'cancelada';

/**
 * Tipos de eventos disponibles
 */
export interface EventType {
    id: string | number;
    name: string;
    autoRegistrationDefault: boolean;
    attendanceMethodDefault: 'manual' | 'kiosco_rapido';
}

/**
 * Estructura de un registro de asistencia para el componente en Strapi.
 */
export interface AttendanceRecord {
    id?: number | string; // ID del registro de asistencia en sí
    sessionId: string | number;
    person: Pick<EventParticipant, 'id' | 'firstName' | 'lastName' | 'ecclesiasticalRole' | 'avatar' | 'email'>; // Referencia al participante
    attended: boolean;
    notes?: string; // Para la razón de ausencia
    partialAttendance?: boolean; // Indica si la asistencia fue parcial
    partialAttendanceComment?: string; // Comentario sobre la asistencia parcial
    timeStayed?: string; // Tiempo aproximado que estuvo en el evento
}

/**
 * Estructura de un evento
 */
export interface Event {
    id: number | string;
    title: string;
    subject?: string; // Grupo/Ministerio
    topic?: string; // Tema Específico
    description?: string; // Podría ser richtext
    date: string; // YYYY-MM-DD
    startTime: string; // HH:mm
    endTime?: string; // HH:mm
    location: string;
    type?: EventType;
    autoRegistration?: boolean;
    status: EventStatus;
    sendNotifications?: boolean;
    invitingDepartment?: string; // Departamento que invita
    
    // En lugar de participants_invited directamente como array de EventParticipant,
    // Strapi devolverá los IDs o los objetos populados.
    // Para el mock, podemos usar EventParticipant[] directamente.
    // Para la API real, la relación `participants_invited` en `event.schema.json`
    // apunta a `plugin::users-permissions.user`.
    participantsInvited: EventParticipant[]; // Para el mock
    
    // Para la API real, esto vendría como un array de componentes.
    // Para el mock, podemos simularlo así.
    attendanceRecords?: AttendanceRecord[];
    sessions: AttendanceSession[];

    // Campo temporal para almacenar el registro de asistencia del usuario actual
    // Solo se usa en el frontend para facilitar la visualización
    userAttendanceRecord?: AttendanceRecord;
}

/**
 * Define la estructura jerárquica de la organización eclesiástica para los filtros.
 * (Esta estructura es principalmente para la lógica del frontend en el prototipo)
 */
export interface EcclesiasticalUnit {
    id: string | number;
    name: string;
    type: 'field' | 'zone' | 'district' | 'church';
    children?: EcclesiasticalUnit[];
    // Otros campos relevantes, como acronym para field
    acronym?: string;
}

/**
 * Estructura para popular los selects de filtros jerárquicos
 */
export type HierarchicalFilterData = {
    fields: Array<{ id: string | number; name: string; acronym?: string, type: string }>;
    zones: Array<{ id: string | number; name: string; fieldId: string | number }>;
    districts: Array<{ id: string | number; name: string; zoneId: string | number }>;
    churches: Array<{ id: string | number; name: string; districtId: string | number }>;
    // Lista de todos los puestos/roles eclesiásticos únicos para el filtro
    ecclesiasticalRoles: string[]; 
};
